using UnityEngine;

namespace EchoesOfTheVoid.Player
{
    /// <summary>
    /// Handles all player input for movement, combat, and abilities
    /// Centralizes input management for easy remapping and consistency
    /// </summary>
    public class PlayerInput : MonoBehaviour
    {
        [Header("Input Settings")]
        [SerializeField] private bool enableInput = true;
        [SerializeField] private float mouseSensitivity = 2f;
        
        // Movement inputs
        private Vector2 moveInput;
        private bool jumpPressed;
        private bool jumpHeld;
        private bool dashPressed;
        private bool runHeld;
        
        // Combat inputs
        private bool meleeAttackPressed;
        private bool meleeAttackHeld;
        private bool magicAttackPressed;
        private bool magicAttackHeld;
        
        // Interaction inputs
        private bool interactPressed;
        private bool inventoryPressed;
        private bool pausePressed;
        
        // Camera inputs
        private Vector2 mouseInput;
        
        // Time manipulation inputs
        private bool timeSlowPressed;
        private bool timeRewindPressed;
        
        private void Update()
        {
            if (!enableInput) return;
            
            HandleMovementInput();
            HandleCombatInput();
            HandleInteractionInput();
            HandleCameraInput();
            HandleTimeManipulationInput();
        }
        
        private void HandleMovementInput()
        {
            // WASD movement
            moveInput = new Vector2(
                Input.GetAxis("Horizontal"),
                Input.GetAxis("Vertical")
            );
            
            // Jump (Space)
            jumpPressed = Input.GetButtonDown("Jump");
            jumpHeld = Input.GetButton("Jump");
            
            // Dash (Left Shift)
            dashPressed = Input.GetKeyDown(KeyCode.LeftShift);
            
            // Run (Left Control)
            runHeld = Input.GetKey(KeyCode.LeftControl);
        }
        
        private void HandleCombatInput()
        {
            // Melee attack (Left Mouse Button)
            meleeAttackPressed = Input.GetMouseButtonDown(0);
            meleeAttackHeld = Input.GetMouseButton(0);
            
            // Magic attack (Right Mouse Button)
            magicAttackPressed = Input.GetMouseButtonDown(1);
            magicAttackHeld = Input.GetMouseButton(1);
        }
        
        private void HandleInteractionInput()
        {
            // Interact (E)
            interactPressed = Input.GetKeyDown(KeyCode.E);
            
            // Inventory (I)
            inventoryPressed = Input.GetKeyDown(KeyCode.I);
            
            // Pause/Menu (Escape)
            pausePressed = Input.GetKeyDown(KeyCode.Escape);
        }
        
        private void HandleCameraInput()
        {
            // Mouse movement for camera
            mouseInput = new Vector2(
                Input.GetAxis("Mouse X") * mouseSensitivity,
                Input.GetAxis("Mouse Y") * mouseSensitivity
            );
        }
        
        private void HandleTimeManipulationInput()
        {
            // Time slow (Q)
            timeSlowPressed = Input.GetKeyDown(KeyCode.Q);
            
            // Time rewind (R)
            timeRewindPressed = Input.GetKeyDown(KeyCode.R);
        }
        
        // Public getters for movement
        public Vector2 GetMoveInput() => moveInput;
        public bool GetJumpPressed() => jumpPressed;
        public bool GetJumpHeld() => jumpHeld;
        public bool GetDashPressed() => dashPressed;
        public bool GetRunHeld() => runHeld;
        
        // Public getters for combat
        public bool GetMeleeAttackPressed() => meleeAttackPressed;
        public bool GetMeleeAttackHeld() => meleeAttackHeld;
        public bool GetMagicAttackPressed() => magicAttackPressed;
        public bool GetMagicAttackHeld() => magicAttackHeld;
        
        // Public getters for interaction
        public bool GetInteractPressed() => interactPressed;
        public bool GetInventoryPressed() => inventoryPressed;
        public bool GetPausePressed() => pausePressed;
        
        // Public getters for camera
        public Vector2 GetMouseInput() => mouseInput;
        
        // Public getters for time manipulation
        public bool GetTimeSlowPressed() => timeSlowPressed;
        public bool GetTimeRewindPressed() => timeRewindPressed;
        
        // Input control methods
        public void EnableInput() => enableInput = true;
        public void DisableInput() => enableInput = false;
        public void SetMouseSensitivity(float sensitivity) => mouseSensitivity = sensitivity;
        
        // Reset all input states (useful for cutscenes, menus, etc.)
        public void ResetInputs()
        {
            moveInput = Vector2.zero;
            jumpPressed = false;
            jumpHeld = false;
            dashPressed = false;
            runHeld = false;
            meleeAttackPressed = false;
            meleeAttackHeld = false;
            magicAttackPressed = false;
            magicAttackHeld = false;
            interactPressed = false;
            inventoryPressed = false;
            pausePressed = false;
            mouseInput = Vector2.zero;
            timeSlowPressed = false;
            timeRewindPressed = false;
        }
    }
}
