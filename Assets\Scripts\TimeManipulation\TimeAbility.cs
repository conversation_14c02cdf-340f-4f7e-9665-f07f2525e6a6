using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace EchoesOfTheVoid.TimeManipulation
{
    /// <summary>
    /// Core time manipulation system for <PERSON><PERSON>'s voidwalker abilities
    /// Handles time slow, time rewind, and temporal effects
    /// </summary>
    public class TimeAbility : MonoBehaviour
    {
        [Header("Time Slow Settings")]
        [SerializeField] private float slowFactor = 0.3f;
        [SerializeField] private float slowDuration = 5f;
        [SerializeField] private float slowCooldown = 8f;
        [SerializeField] private float slowManayCost = 30f;
        
        [Header("Time Rewind Settings")]
        [SerializeField] private float rewindDuration = 3f;
        [SerializeField] private float rewindCooldown = 15f;
        [SerializeField] private float rewindManaCost = 50f;
        [SerializeField] private int maxRewindFrames = 180; // 3 seconds at 60fps
        
        [Header("Temporal Blink Settings")]
        [SerializeField] private float blinkDistance = 10f;
        [SerializeField] private float blinkCooldown = 5f;
        [SerializeField] private float blinkManaCost = 20f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip timeSlowSound;
        [SerializeField] private AudioClip timeRewindSound;
        [SerializeField] private AudioClip temporalBlinkSound;
        
        // Components
        private PlayerInput playerInput;
        private AudioSource audioSource;
        
        // Time slow state
        private bool isTimeSlowed = false;
        private float slowTimer = 0f;
        private float slowCooldownTimer = 0f;
        
        // Time rewind state
        private bool isRewinding = false;
        private float rewindTimer = 0f;
        private float rewindCooldownTimer = 0f;
        private Queue<TimeSnapshot> timeSnapshots = new Queue<TimeSnapshot>();
        
        // Temporal blink state
        private float blinkCooldownTimer = 0f;
        
        // Player stats (would normally come from a player stats system)
        [SerializeField] private float currentMana = 100f;
        [SerializeField] private float maxMana = 100f;
        
        private void Awake()
        {
            playerInput = GetComponent<PlayerInput>();
            audioSource = GetComponent<AudioSource>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        private void Update()
        {
            HandleInput();
            UpdateTimeSlow();
            UpdateTimeRewind();
            UpdateCooldowns();
            RecordTimeSnapshots();
        }
        
        private void HandleInput()
        {
            if (playerInput == null) return;
            
            // Time slow input
            if (playerInput.GetTimeSlowPressed())
            {
                ToggleTimeSlow();
            }
            
            // Time rewind input
            if (playerInput.GetTimeRewindPressed())
            {
                StartTimeRewind();
            }
            
            // Temporal blink (could be mapped to a different key)
            if (Input.GetKeyDown(KeyCode.T))
            {
                PerformTemporalBlink();
            }
        }
        
        private void ToggleTimeSlow()
        {
            if (isTimeSlowed)
            {
                StopTimeSlow();
            }
            else if (CanUseTimeSlow())
            {
                StartTimeSlow();
            }
        }
        
        private bool CanUseTimeSlow()
        {
            return !isTimeSlowed && slowCooldownTimer <= 0 && currentMana >= slowManayCost && !isRewinding;
        }
        
        private void StartTimeSlow()
        {
            isTimeSlowed = true;
            slowTimer = slowDuration;
            slowCooldownTimer = slowCooldown;
            currentMana -= slowManayCost;
            
            // Apply time scale
            Time.timeScale = slowFactor;
            Time.fixedDeltaTime = 0.02f * Time.timeScale;
            
            // Play sound effect
            if (timeSlowSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(timeSlowSound);
            }
            
            // Visual effects could be triggered here
            OnTimeSlowStarted();
        }
        
        private void StopTimeSlow()
        {
            isTimeSlowed = false;
            
            // Reset time scale
            Time.timeScale = 1f;
            Time.fixedDeltaTime = 0.02f;
            
            OnTimeSlowEnded();
        }
        
        private void UpdateTimeSlow()
        {
            if (isTimeSlowed)
            {
                slowTimer -= Time.unscaledDeltaTime; // Use unscaled time
                
                if (slowTimer <= 0)
                {
                    StopTimeSlow();
                }
            }
        }
        
        private void StartTimeRewind()
        {
            if (!CanUseTimeRewind()) return;
            
            isRewinding = true;
            rewindTimer = rewindDuration;
            rewindCooldownTimer = rewindCooldown;
            currentMana -= rewindManaCost;
            
            // Stop time slow if active
            if (isTimeSlowed)
            {
                StopTimeSlow();
            }
            
            // Play sound effect
            if (timeRewindSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(timeRewindSound);
            }
            
            OnTimeRewindStarted();
        }
        
        private bool CanUseTimeRewind()
        {
            return !isRewinding && rewindCooldownTimer <= 0 && currentMana >= rewindManaCost && timeSnapshots.Count > 0;
        }
        
        private void UpdateTimeRewind()
        {
            if (isRewinding)
            {
                rewindTimer -= Time.deltaTime;
                
                // Apply rewind effect
                if (timeSnapshots.Count > 0)
                {
                    TimeSnapshot snapshot = timeSnapshots.Dequeue();
                    transform.position = snapshot.position;
                    transform.rotation = snapshot.rotation;
                }
                
                if (rewindTimer <= 0 || timeSnapshots.Count == 0)
                {
                    StopTimeRewind();
                }
            }
        }
        
        private void StopTimeRewind()
        {
            isRewinding = false;
            OnTimeRewindEnded();
        }
        
        private void PerformTemporalBlink()
        {
            if (!CanUseTemporalBlink()) return;
            
            blinkCooldownTimer = blinkCooldown;
            currentMana -= blinkManaCost;
            
            // Calculate blink position
            Vector3 blinkDirection = transform.forward;
            Vector3 targetPosition = transform.position + blinkDirection * blinkDistance;
            
            // Raycast to ensure we don't blink into walls
            if (Physics.Raycast(transform.position, blinkDirection, out RaycastHit hit, blinkDistance))
            {
                targetPosition = hit.point - blinkDirection * 1f; // Stop 1 unit before the wall
            }
            
            // Perform the blink
            transform.position = targetPosition;
            
            // Play sound effect
            if (temporalBlinkSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(temporalBlinkSound);
            }
            
            OnTemporalBlinkPerformed();
        }
        
        private bool CanUseTemporalBlink()
        {
            return blinkCooldownTimer <= 0 && currentMana >= blinkManaCost && !isRewinding;
        }
        
        private void RecordTimeSnapshots()
        {
            if (!isRewinding) // Don't record while rewinding
            {
                timeSnapshots.Enqueue(new TimeSnapshot
                {
                    position = transform.position,
                    rotation = transform.rotation,
                    timestamp = Time.time
                });
                
                // Limit the number of snapshots
                while (timeSnapshots.Count > maxRewindFrames)
                {
                    timeSnapshots.Dequeue();
                }
            }
        }
        
        private void UpdateCooldowns()
        {
            if (slowCooldownTimer > 0)
                slowCooldownTimer -= Time.deltaTime;
            
            if (rewindCooldownTimer > 0)
                rewindCooldownTimer -= Time.deltaTime;
            
            if (blinkCooldownTimer > 0)
                blinkCooldownTimer -= Time.deltaTime;
        }
        
        // Event methods for other systems to hook into
        protected virtual void OnTimeSlowStarted() { }
        protected virtual void OnTimeSlowEnded() { }
        protected virtual void OnTimeRewindStarted() { }
        protected virtual void OnTimeRewindEnded() { }
        protected virtual void OnTemporalBlinkPerformed() { }
        
        // Public getters for UI and other systems
        public bool IsTimeSlowed => isTimeSlowed;
        public bool IsRewinding => isRewinding;
        public float TimeSlowCooldownProgress => 1f - (slowCooldownTimer / slowCooldown);
        public float TimeRewindCooldownProgress => 1f - (rewindCooldownTimer / rewindCooldown);
        public float TemporalBlinkCooldownProgress => 1f - (blinkCooldownTimer / blinkCooldown);
        public float CurrentMana => currentMana;
        public float MaxMana => maxMana;
        
        // Mana management (would normally be in a separate player stats system)
        public void RestoreMana(float amount)
        {
            currentMana = Mathf.Min(currentMana + amount, maxMana);
        }
        
        public void SetMana(float amount)
        {
            currentMana = Mathf.Clamp(amount, 0, maxMana);
        }
    }
    
    [System.Serializable]
    public struct TimeSnapshot
    {
        public Vector3 position;
        public Quaternion rotation;
        public float timestamp;
    }
}
