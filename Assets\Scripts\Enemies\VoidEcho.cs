using UnityEngine;
using UnityEngine.AI;
using System.Collections;

namespace EchoesOfTheVoid.Enemies
{
    /// <summary>
    /// Void Echo enemy - shadowy remnants of destroyed timelines
    /// Basic AI with patrol, chase, and attack behaviors
    /// </summary>
    [RequireComponent(typeof(NavMeshAgent))]
    public class VoidEcho : MonoBehaviour, Combat.IDamageable
    {
        [Header("Stats")]
        [SerializeField] private float maxHealth = 50f;
        [SerializeField] private float currentHealth = 50f;
        [SerializeField] private float damage = 15f;
        [SerializeField] private float attackRange = 2f;
        [SerializeField] private float detectionRange = 10f;
        [SerializeField] private float attackCooldown = 2f;
        
        [Header("Movement")]
        [SerializeField] private float patrolSpeed = 2f;
        [SerializeField] private float chaseSpeed = 4f;
        [SerializeField] private float patrolRadius = 5f;
        [SerializeField] private float waitTime = 3f;
        
        [Header("Audio")]
        [SerializeField] private AudioClip[] attackSounds;
        [SerializeField] private AudioClip[] hurtSounds;
        [SerializeField] private AudioClip deathSound;
        
        [Header("Effects")]
        [SerializeField] private GameObject deathEffect;
        [SerializeField] private GameObject attackEffect;
        
        // Components
        private NavMeshAgent agent;
        private Animator animator;
        private AudioSource audioSource;
        
        // AI State
        public enum EnemyState
        {
            Patrol,
            Chase,
            Attack,
            Hurt,
            Dead
        }
        
        [SerializeField] private EnemyState currentState = EnemyState.Patrol;
        
        // AI variables
        private Transform player;
        private Vector3 startPosition;
        private Vector3 patrolTarget;
        private float lastAttackTime;
        private float waitTimer;
        private bool isWaiting;
        
        // Combat
        private bool isDead = false;
        
        private void Awake()
        {
            agent = GetComponent<NavMeshAgent>();
            animator = GetComponent<Animator>();
            audioSource = GetComponent<AudioSource>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            startPosition = transform.position;
            currentHealth = maxHealth;
        }
        
        private void Start()
        {
            // Find player
            GameObject playerObj = GameManager.Instance.CurrentPlayer;
            if (playerObj != null)
            {
                player = playerObj.transform;
            }
            
            // Set initial patrol target
            SetNewPatrolTarget();
            
            // Set initial agent speed
            agent.speed = patrolSpeed;
        }
        
        private void Update()
        {
            if (isDead) return;
            
            UpdateAI();
            UpdateAnimations();
        }
        
        private void UpdateAI()
        {
            float distanceToPlayer = player != null ? Vector3.Distance(transform.position, player.position) : float.MaxValue;
            
            switch (currentState)
            {
                case EnemyState.Patrol:
                    HandlePatrolState(distanceToPlayer);
                    break;
                    
                case EnemyState.Chase:
                    HandleChaseState(distanceToPlayer);
                    break;
                    
                case EnemyState.Attack:
                    HandleAttackState(distanceToPlayer);
                    break;
                    
                case EnemyState.Hurt:
                    HandleHurtState();
                    break;
            }
        }
        
        private void HandlePatrolState(float distanceToPlayer)
        {
            // Check if player is in detection range
            if (distanceToPlayer <= detectionRange)
            {
                ChangeState(EnemyState.Chase);
                return;
            }
            
            // Handle patrol movement
            if (!isWaiting)
            {
                if (!agent.pathPending && agent.remainingDistance < 0.5f)
                {
                    // Reached patrol point, wait before moving to next
                    isWaiting = true;
                    waitTimer = waitTime;
                }
            }
            else
            {
                waitTimer -= Time.deltaTime;
                if (waitTimer <= 0)
                {
                    SetNewPatrolTarget();
                    isWaiting = false;
                }
            }
        }
        
        private void HandleChaseState(float distanceToPlayer)
        {
            if (player == null)
            {
                ChangeState(EnemyState.Patrol);
                return;
            }
            
            // Check if player is too far away
            if (distanceToPlayer > detectionRange * 1.5f)
            {
                ChangeState(EnemyState.Patrol);
                return;
            }
            
            // Check if in attack range
            if (distanceToPlayer <= attackRange)
            {
                ChangeState(EnemyState.Attack);
                return;
            }
            
            // Chase player
            agent.SetDestination(player.position);
        }
        
        private void HandleAttackState(float distanceToPlayer)
        {
            if (player == null)
            {
                ChangeState(EnemyState.Patrol);
                return;
            }
            
            // Check if player moved out of attack range
            if (distanceToPlayer > attackRange)
            {
                ChangeState(EnemyState.Chase);
                return;
            }
            
            // Face player
            Vector3 direction = (player.position - transform.position).normalized;
            transform.rotation = Quaternion.LookRotation(direction);
            
            // Attack if cooldown is ready
            if (Time.time - lastAttackTime >= attackCooldown)
            {
                PerformAttack();
            }
        }
        
        private void HandleHurtState()
        {
            // Simple hurt state - could add knockback or stun here
            // Automatically return to appropriate state after hurt animation
            if (player != null && Vector3.Distance(transform.position, player.position) <= attackRange)
            {
                ChangeState(EnemyState.Attack);
            }
            else if (player != null && Vector3.Distance(transform.position, player.position) <= detectionRange)
            {
                ChangeState(EnemyState.Chase);
            }
            else
            {
                ChangeState(EnemyState.Patrol);
            }
        }
        
        private void ChangeState(EnemyState newState)
        {
            if (currentState == newState) return;
            
            currentState = newState;
            
            switch (newState)
            {
                case EnemyState.Patrol:
                    agent.speed = patrolSpeed;
                    agent.stoppingDistance = 0;
                    SetNewPatrolTarget();
                    break;
                    
                case EnemyState.Chase:
                    agent.speed = chaseSpeed;
                    agent.stoppingDistance = attackRange * 0.8f;
                    break;
                    
                case EnemyState.Attack:
                    agent.speed = 0;
                    agent.ResetPath();
                    break;
                    
                case EnemyState.Hurt:
                    agent.speed = 0;
                    agent.ResetPath();
                    break;
            }
        }
        
        private void SetNewPatrolTarget()
        {
            Vector3 randomDirection = Random.insideUnitSphere * patrolRadius;
            randomDirection += startPosition;
            
            NavMeshHit hit;
            if (NavMesh.SamplePosition(randomDirection, out hit, patrolRadius, 1))
            {
                patrolTarget = hit.position;
                agent.SetDestination(patrolTarget);
            }
        }
        
        private void PerformAttack()
        {
            lastAttackTime = Time.time;
            
            // Play attack animation
            if (animator != null)
            {
                animator.SetTrigger("Attack");
            }
            
            // Play attack sound
            PlayRandomSound(attackSounds);
            
            // Deal damage to player
            if (player != null)
            {
                PlayerHealth playerHealth = player.GetComponent<PlayerHealth>();
                if (playerHealth != null)
                {
                    playerHealth.TakeDamage(damage);
                }
            }
            
            // Spawn attack effect
            if (attackEffect != null)
            {
                Instantiate(attackEffect, transform.position + transform.forward, transform.rotation);
            }
        }
        
        private void UpdateAnimations()
        {
            if (animator == null) return;
            
            // Set animation parameters
            animator.SetFloat("Speed", agent.velocity.magnitude);
            animator.SetBool("IsChasing", currentState == EnemyState.Chase);
            animator.SetBool("IsAttacking", currentState == EnemyState.Attack);
            animator.SetBool("IsDead", isDead);
        }
        
        public void TakeDamage(Combat.DamageInfo damageInfo)
        {
            if (isDead) return;
            
            currentHealth -= damageInfo.damage;
            
            // Play hurt sound
            PlayRandomSound(hurtSounds);
            
            // Change to hurt state briefly
            ChangeState(EnemyState.Hurt);
            
            // Check if dead
            if (currentHealth <= 0)
            {
                Die();
            }
        }
        
        private void Die()
        {
            isDead = true;
            currentState = EnemyState.Dead;
            
            // Stop agent
            agent.enabled = false;
            
            // Play death sound
            if (deathSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(deathSound);
            }
            
            // Spawn death effect
            if (deathEffect != null)
            {
                Instantiate(deathEffect, transform.position, transform.rotation);
            }
            
            // Play death animation
            if (animator != null)
            {
                animator.SetTrigger("Die");
            }
            
            // Destroy after delay
            StartCoroutine(DestroyAfterDelay(3f));
        }
        
        private IEnumerator DestroyAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            Destroy(gameObject);
        }
        
        private void PlayRandomSound(AudioClip[] sounds)
        {
            if (sounds != null && sounds.Length > 0 && audioSource != null)
            {
                AudioClip randomClip = sounds[Random.Range(0, sounds.Length)];
                if (randomClip != null)
                {
                    audioSource.PlayOneShot(randomClip);
                }
            }
        }
        
        // Public getters
        public float HealthPercentage => currentHealth / maxHealth;
        public bool IsDead => isDead;
        public EnemyState CurrentState => currentState;
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            // Detection range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // Attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);
            
            // Patrol radius
            Gizmos.color = Color.blue;
            Vector3 patrolCenter = Application.isPlaying ? startPosition : transform.position;
            Gizmos.DrawWireSphere(patrolCenter, patrolRadius);
            
            // Current target
            if (Application.isPlaying && currentState == EnemyState.Patrol)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(patrolTarget, 0.5f);
                Gizmos.DrawLine(transform.position, patrolTarget);
            }
        }
    }
}
