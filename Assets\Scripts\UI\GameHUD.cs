using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace EchoesOfTheVoid.UI
{
    /// <summary>
    /// Main game HUD displaying health, mana, abilities, and other vital information
    /// </summary>
    public class GameHUD : MonoBehaviour
    {
        [Header("Health UI")]
        [SerializeField] private Slider healthBar;
        [SerializeField] private TextMeshProUGUI healthText;
        [SerializeField] private Image healthFill;
        [SerializeField] private Color healthColor = Color.red;
        [SerializeField] private Color lowHealthColor = Color.darkRed;
        [SerializeField] private float lowHealthThreshold = 0.3f;
        
        [Header("Mana UI")]
        [SerializeField] private Slider manaBar;
        [SerializeField] private TextMeshProUGUI manaText;
        [SerializeField] private Image manaFill;
        [SerializeField] private Color manaColor = Color.blue;
        
        [Header("Ability Cooldowns")]
        [SerializeField] private Image timeSlowIcon;
        [SerializeField] private Image timeRewindIcon;
        [SerializeField] private Image dashIcon;
        [SerializeField] private Image magicAttackIcon;
        [SerializeField] private TextMesh<PERSON><PERSON>UG<PERSON> timeSlowCooldownText;
        [SerializeField] private TextMeshProUGUI timeRewindCooldownText;
        [SerializeField] private TextMeshProUGUI dashCooldownText;
        [SerializeField] private TextMeshProUGUI magicCooldownText;
        
        [Header("Combat UI")]
        [SerializeField] private TextMeshProUGUI comboText;
        [SerializeField] private GameObject comboDisplay;
        [SerializeField] private float comboDisplayDuration = 2f;
        
        [Header("Interaction UI")]
        [SerializeField] private GameObject interactionPrompt;
        [SerializeField] private TextMeshProUGUI interactionText;
        
        [Header("Status Effects")]
        [SerializeField] private GameObject timeSlowEffect;
        [SerializeField] private GameObject rewindEffect;
        
        // Component references
        private PlayerHealth playerHealth;
        private TimeAbility timeAbility;
        private PlayerCombat playerCombat;
        private PlayerMovement playerMovement;
        
        // UI state
        private float comboDisplayTimer;
        private bool isComboDisplayActive;
        
        private void Start()
        {
            // Find player components when player spawns
            GameManager.Instance.OnPlayerSpawned += FindPlayerComponents;
            FindPlayerComponents();
            
            // Initialize UI
            InitializeUI();
        }
        
        private void Update()
        {
            UpdateHealthUI();
            UpdateManaUI();
            UpdateAbilityCooldowns();
            UpdateCombatUI();
            UpdateStatusEffects();
            UpdateComboDisplay();
        }
        
        private void FindPlayerComponents()
        {
            GameObject player = GameManager.Instance.CurrentPlayer;
            if (player != null)
            {
                playerHealth = player.GetComponent<PlayerHealth>();
                timeAbility = player.GetComponent<TimeAbility>();
                playerCombat = player.GetComponent<PlayerCombat>();
                playerMovement = player.GetComponent<PlayerMovement>();
            }
        }
        
        private void InitializeUI()
        {
            // Set initial colors
            if (healthFill != null)
                healthFill.color = healthColor;
            
            if (manaFill != null)
                manaFill.color = manaColor;
            
            // Hide combo display initially
            if (comboDisplay != null)
                comboDisplay.SetActive(false);
            
            // Hide interaction prompt initially
            if (interactionPrompt != null)
                interactionPrompt.SetActive(false);
            
            // Hide status effects initially
            if (timeSlowEffect != null)
                timeSlowEffect.SetActive(false);
            
            if (rewindEffect != null)
                rewindEffect.SetActive(false);
        }
        
        private void UpdateHealthUI()
        {
            if (playerHealth == null) return;
            
            float healthPercentage = playerHealth.CurrentHealth / playerHealth.MaxHealth;
            
            // Update health bar
            if (healthBar != null)
                healthBar.value = healthPercentage;
            
            // Update health text
            if (healthText != null)
                healthText.text = $"{playerHealth.CurrentHealth:F0}/{playerHealth.MaxHealth:F0}";
            
            // Update health color based on percentage
            if (healthFill != null)
            {
                Color targetColor = healthPercentage <= lowHealthThreshold ? lowHealthColor : healthColor;
                healthFill.color = Color.Lerp(healthFill.color, targetColor, Time.deltaTime * 5f);
            }
        }
        
        private void UpdateManaUI()
        {
            if (timeAbility == null) return;
            
            float manaPercentage = timeAbility.CurrentMana / timeAbility.MaxMana;
            
            // Update mana bar
            if (manaBar != null)
                manaBar.value = manaPercentage;
            
            // Update mana text
            if (manaText != null)
                manaText.text = $"{timeAbility.CurrentMana:F0}/{timeAbility.MaxMana:F0}";
        }
        
        private void UpdateAbilityCooldowns()
        {
            if (timeAbility == null || playerMovement == null || playerCombat == null) return;
            
            // Time slow cooldown
            UpdateCooldownIcon(timeSlowIcon, timeSlowCooldownText, timeAbility.TimeSlowCooldownProgress);
            
            // Time rewind cooldown
            UpdateCooldownIcon(timeRewindIcon, timeRewindCooldownText, timeAbility.TimeRewindCooldownProgress);
            
            // Dash cooldown
            UpdateCooldownIcon(dashIcon, dashCooldownText, playerMovement.DashCooldownProgress);
            
            // Magic attack cooldown
            UpdateCooldownIcon(magicAttackIcon, magicCooldownText, playerCombat.MagicCooldownProgress);
        }
        
        private void UpdateCooldownIcon(Image icon, TextMeshProUGUI cooldownText, float progress)
        {
            if (icon != null)
            {
                icon.fillAmount = progress;
                icon.color = progress >= 1f ? Color.white : Color.gray;
            }
            
            if (cooldownText != null)
            {
                if (progress < 1f)
                {
                    float remainingTime = (1f - progress) * 10f; // Approximate remaining time
                    cooldownText.text = remainingTime.ToString("F1");
                    cooldownText.gameObject.SetActive(true);
                }
                else
                {
                    cooldownText.gameObject.SetActive(false);
                }
            }
        }
        
        private void UpdateCombatUI()
        {
            if (playerCombat == null) return;
            
            // Update combo display
            if (playerCombat.CurrentComboCount > 1)
            {
                ShowComboDisplay(playerCombat.CurrentComboCount);
            }
        }
        
        private void ShowComboDisplay(int comboCount)
        {
            if (comboDisplay != null && comboText != null)
            {
                comboDisplay.SetActive(true);
                comboText.text = $"COMBO x{comboCount}";
                comboDisplayTimer = comboDisplayDuration;
                isComboDisplayActive = true;
            }
        }
        
        private void UpdateComboDisplay()
        {
            if (isComboDisplayActive)
            {
                comboDisplayTimer -= Time.deltaTime;
                
                if (comboDisplayTimer <= 0)
                {
                    if (comboDisplay != null)
                        comboDisplay.SetActive(false);
                    
                    isComboDisplayActive = false;
                }
            }
        }
        
        private void UpdateStatusEffects()
        {
            if (timeAbility == null) return;
            
            // Time slow effect
            if (timeSlowEffect != null)
                timeSlowEffect.SetActive(timeAbility.IsTimeSlowed);
            
            // Time rewind effect
            if (rewindEffect != null)
                rewindEffect.SetActive(timeAbility.IsRewinding);
        }
        
        public void ShowInteractionPrompt(string text)
        {
            if (interactionPrompt != null && interactionText != null)
            {
                interactionPrompt.SetActive(true);
                interactionText.text = text;
            }
        }
        
        public void HideInteractionPrompt()
        {
            if (interactionPrompt != null)
                interactionPrompt.SetActive(false);
        }
        
        // Public methods for other systems to update UI
        public void ShowDamageIndicator(float damage, Vector3 worldPosition)
        {
            // TODO: Implement floating damage text
        }
        
        public void ShowStatusMessage(string message, float duration = 3f)
        {
            // TODO: Implement status message system
        }
        
        public void SetHealthBarVisible(bool visible)
        {
            if (healthBar != null)
                healthBar.gameObject.SetActive(visible);
        }
        
        public void SetManaBarVisible(bool visible)
        {
            if (manaBar != null)
                manaBar.gameObject.SetActive(visible);
        }
        
        private void OnDestroy()
        {
            if (GameManager.Instance != null)
                GameManager.Instance.OnPlayerSpawned -= FindPlayerComponents;
        }
    }
    
    // Placeholder for PlayerHealth component
    public class PlayerHealth : MonoBehaviour
    {
        [SerializeField] private float maxHealth = 100f;
        [SerializeField] private float currentHealth = 100f;
        
        public float MaxHealth => maxHealth;
        public float CurrentHealth => currentHealth;
        
        public void TakeDamage(float damage)
        {
            currentHealth = Mathf.Max(0, currentHealth - damage);
            
            if (currentHealth <= 0)
            {
                Die();
            }
        }
        
        public void Heal(float amount)
        {
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
        }
        
        private void Die()
        {
            // Handle player death
            GameManager.Instance.GameOver();
        }
    }
}
