using UnityEngine;
using System.Collections;

namespace EchoesOfTheVoid.Combat
{
    /// <summary>
    /// Handles player combat including melee attacks, magic abilities, and combo system
    /// </summary>
    public class PlayerCombat : MonoBehaviour
    {
        [Header("Melee Combat")]
        [SerializeField] private float meleeRange = 2f;
        [SerializeField] private float meleeDamage = 25f;
        [SerializeField] private float meleeAttackSpeed = 1f;
        [SerializeField] private LayerMask enemyLayers = 1;
        
        [Header("Magic Combat")]
        [SerializeField] private float magicRange = 10f;
        [SerializeField] private float magicDamage = 35f;
        [SerializeField] private float magicCooldown = 2f;
        [SerializeField] private float magicManaCost = 25f;
        
        [Header("Combo System")]
        [SerializeField] private float comboWindow = 1.5f;
        [SerializeField] private int maxComboCount = 3;
        [SerializeField] private float comboDamageMultiplier = 1.2f;
        
        [Header("Attack Points")]
        [SerializeField] private Transform meleeAttackPoint;
        [SerializeField] private Transform magicAttackPoint;
        
        [Header("Audio")]
        [SerializeField] private AudioClip[] meleeSounds;
        [SerializeField] private AudioClip[] magicSounds;
        [SerializeField] private AudioClip comboFinisherSound;
        
        // Components
        private PlayerInput playerInput;
        private TimeAbility timeAbility;
        private AudioSource audioSource;
        private Animator animator;
        
        // Combat state
        private bool isAttacking = false;
        private bool canAttack = true;
        private float lastAttackTime;
        private int currentComboCount = 0;
        private float magicCooldownTimer = 0f;
        
        // Attack detection
        private Collider[] hitEnemies = new Collider[10];
        
        private void Awake()
        {
            playerInput = GetComponent<PlayerInput>();
            timeAbility = GetComponent<TimeAbility>();
            audioSource = GetComponent<AudioSource>();
            animator = GetComponent<Animator>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            // Create attack points if not assigned
            if (meleeAttackPoint == null)
            {
                GameObject meleePoint = new GameObject("MeleeAttackPoint");
                meleePoint.transform.SetParent(transform);
                meleePoint.transform.localPosition = new Vector3(0, 1f, 1f);
                meleeAttackPoint = meleePoint.transform;
            }
            
            if (magicAttackPoint == null)
            {
                GameObject magicPoint = new GameObject("MagicAttackPoint");
                magicPoint.transform.SetParent(transform);
                magicPoint.transform.localPosition = new Vector3(0, 1.5f, 0.5f);
                magicAttackPoint = magicPoint.transform;
            }
        }
        
        private void Update()
        {
            HandleInput();
            UpdateCombat();
            UpdateCooldowns();
        }
        
        private void HandleInput()
        {
            if (playerInput == null) return;
            
            // Melee attack
            if (playerInput.GetMeleeAttackPressed() && canAttack)
            {
                PerformMeleeAttack();
            }
            
            // Magic attack
            if (playerInput.GetMagicAttackPressed() && CanUseMagic())
            {
                PerformMagicAttack();
            }
        }
        
        private void PerformMeleeAttack()
        {
            if (!canAttack || isAttacking) return;
            
            StartCoroutine(MeleeAttackCoroutine());
        }
        
        private IEnumerator MeleeAttackCoroutine()
        {
            isAttacking = true;
            canAttack = false;
            
            // Check for combo
            bool isComboAttack = Time.time - lastAttackTime <= comboWindow && currentComboCount < maxComboCount;
            
            if (isComboAttack)
            {
                currentComboCount++;
            }
            else
            {
                currentComboCount = 1;
            }
            
            lastAttackTime = Time.time;
            
            // Play animation
            if (animator != null)
            {
                animator.SetTrigger($"MeleeAttack{currentComboCount}");
            }
            
            // Wait for attack timing
            yield return new WaitForSeconds(0.3f / meleeAttackSpeed);
            
            // Perform attack detection
            DetectMeleeHits();
            
            // Play sound
            PlayRandomSound(meleeSounds);
            
            // Wait for attack recovery
            yield return new WaitForSeconds(0.7f / meleeAttackSpeed);
            
            isAttacking = false;
            canAttack = true;
            
            // Reset combo if window expires
            if (Time.time - lastAttackTime > comboWindow)
            {
                currentComboCount = 0;
            }
        }
        
        private void DetectMeleeHits()
        {
            int hitCount = Physics.OverlapSphereNonAlloc(meleeAttackPoint.position, meleeRange, hitEnemies, enemyLayers);
            
            float finalDamage = meleeDamage;
            if (currentComboCount > 1)
            {
                finalDamage *= Mathf.Pow(comboDamageMultiplier, currentComboCount - 1);
            }
            
            for (int i = 0; i < hitCount; i++)
            {
                IDamageable damageable = hitEnemies[i].GetComponent<IDamageable>();
                if (damageable != null)
                {
                    DamageInfo damageInfo = new DamageInfo
                    {
                        damage = finalDamage,
                        source = gameObject,
                        damageType = DamageType.Melee,
                        isComboAttack = currentComboCount > 1,
                        comboCount = currentComboCount
                    };
                    
                    damageable.TakeDamage(damageInfo);
                }
            }
            
            // Play combo finisher sound
            if (currentComboCount == maxComboCount && comboFinisherSound != null)
            {
                audioSource.PlayOneShot(comboFinisherSound);
            }
        }
        
        private void PerformMagicAttack()
        {
            if (!CanUseMagic()) return;
            
            magicCooldownTimer = magicCooldown;
            
            // Consume mana
            if (timeAbility != null)
            {
                timeAbility.SetMana(timeAbility.CurrentMana - magicManaCost);
            }
            
            // Play animation
            if (animator != null)
            {
                animator.SetTrigger("MagicAttack");
            }
            
            // Perform magic attack
            StartCoroutine(MagicAttackCoroutine());
        }
        
        private IEnumerator MagicAttackCoroutine()
        {
            // Wait for cast time
            yield return new WaitForSeconds(0.5f);
            
            // Create magic projectile or effect
            DetectMagicHits();
            
            // Play sound
            PlayRandomSound(magicSounds);
        }
        
        private void DetectMagicHits()
        {
            // Raycast for magic attack
            if (Physics.Raycast(magicAttackPoint.position, transform.forward, out RaycastHit hit, magicRange, enemyLayers))
            {
                IDamageable damageable = hit.collider.GetComponent<IDamageable>();
                if (damageable != null)
                {
                    DamageInfo damageInfo = new DamageInfo
                    {
                        damage = magicDamage,
                        source = gameObject,
                        damageType = DamageType.Magic,
                        hitPoint = hit.point,
                        hitNormal = hit.normal
                    };
                    
                    damageable.TakeDamage(damageInfo);
                }
            }
        }
        
        private bool CanUseMagic()
        {
            return magicCooldownTimer <= 0 && 
                   timeAbility != null && 
                   timeAbility.CurrentMana >= magicManaCost &&
                   !isAttacking;
        }
        
        private void UpdateCombat()
        {
            // Reset combo if too much time has passed
            if (Time.time - lastAttackTime > comboWindow && currentComboCount > 0)
            {
                currentComboCount = 0;
            }
        }
        
        private void UpdateCooldowns()
        {
            if (magicCooldownTimer > 0)
            {
                magicCooldownTimer -= Time.deltaTime;
            }
        }
        
        private void PlayRandomSound(AudioClip[] sounds)
        {
            if (sounds != null && sounds.Length > 0 && audioSource != null)
            {
                AudioClip randomClip = sounds[Random.Range(0, sounds.Length)];
                if (randomClip != null)
                {
                    audioSource.PlayOneShot(randomClip);
                }
            }
        }
        
        // Public getters for UI and other systems
        public bool IsAttacking => isAttacking;
        public int CurrentComboCount => currentComboCount;
        public float MagicCooldownProgress => 1f - (magicCooldownTimer / magicCooldown);
        public bool CanPerformMelee => canAttack && !isAttacking;
        public bool CanPerformMagic => CanUseMagic();
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            if (meleeAttackPoint != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(meleeAttackPoint.position, meleeRange);
            }
            
            if (magicAttackPoint != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(magicAttackPoint.position, transform.forward * magicRange);
            }
        }
    }
    
    // Damage system interfaces and structs
    public interface IDamageable
    {
        void TakeDamage(DamageInfo damageInfo);
    }
    
    [System.Serializable]
    public struct DamageInfo
    {
        public float damage;
        public GameObject source;
        public DamageType damageType;
        public Vector3 hitPoint;
        public Vector3 hitNormal;
        public bool isComboAttack;
        public int comboCount;
    }
    
    public enum DamageType
    {
        Melee,
        Magic,
        Environmental,
        Time
    }
}
