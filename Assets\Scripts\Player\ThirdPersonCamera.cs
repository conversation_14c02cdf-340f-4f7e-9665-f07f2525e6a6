using UnityEngine;

namespace EchoesOfTheVoid.Player
{
    /// <summary>
    /// Third-person camera controller for following the player
    /// <PERSON><PERSON> smooth camera movement, rotation, and collision detection
    /// </summary>
    public class ThirdPersonCamera : MonoBehaviour
    {
        [Header("Camera Settings")]
        [SerializeField] private Transform target; // Player transform
        [SerializeField] private float distance = 5f;
        [SerializeField] private float height = 2f;
        [SerializeField] private float rotationSpeed = 2f;
        [SerializeField] private float followSpeed = 10f;
        
        [Header("Camera Limits")]
        [SerializeField] private float minVerticalAngle = -30f;
        [SerializeField] private float maxVerticalAngle = 60f;
        
        [Header("Collision Detection")]
        [SerializeField] private bool enableCollision = true;
        [SerializeField] private float collisionRadius = 0.3f;
        [SerializeField] private LayerMask collisionLayers = 1;
        [SerializeField] private float collisionOffset = 0.1f;
        
        [Header("Smoothing")]
        [SerializeField] private float positionSmoothTime = 0.1f;
        [SerializeField] private float rotationSmoothTime = 0.1f;
        
        // Private variables
        private PlayerInput playerInput;
        private float currentX = 0f;
        private float currentY = 0f;
        private Vector3 currentVelocity;
        private Vector3 desiredPosition;
        private float currentDistance;
        
        // Camera shake
        private Vector3 shakeOffset;
        private float shakeIntensity;
        private float shakeDuration;
        
        private void Awake()
        {
            // Find player input if not assigned
            if (target != null)
            {
                playerInput = target.GetComponent<PlayerInput>();
            }
            
            currentDistance = distance;
            
            // Lock cursor to center of screen
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        
        private void Start()
        {
            // Initialize camera rotation based on current target rotation
            if (target != null)
            {
                Vector3 angles = transform.eulerAngles;
                currentX = angles.y;
                currentY = angles.x;
            }
        }
        
        private void LateUpdate()
        {
            if (target == null) return;
            
            HandleInput();
            CalculateDesiredPosition();
            HandleCollision();
            UpdateCameraPosition();
            UpdateCameraShake();
        }
        
        private void HandleInput()
        {
            if (playerInput != null)
            {
                Vector2 mouseInput = playerInput.GetMouseInput();
                currentX += mouseInput.x;
                currentY -= mouseInput.y;
            }
            else
            {
                // Fallback to direct input
                currentX += Input.GetAxis("Mouse X") * rotationSpeed;
                currentY -= Input.GetAxis("Mouse Y") * rotationSpeed;
            }
            
            // Clamp vertical rotation
            currentY = Mathf.Clamp(currentY, minVerticalAngle, maxVerticalAngle);
        }
        
        private void CalculateDesiredPosition()
        {
            // Calculate the desired position based on target position and camera angles
            Quaternion rotation = Quaternion.Euler(currentY, currentX, 0);
            Vector3 offset = rotation * new Vector3(0, height, -currentDistance);
            desiredPosition = target.position + offset;
        }
        
        private void HandleCollision()
        {
            if (!enableCollision) return;
            
            Vector3 direction = (desiredPosition - target.position).normalized;
            float targetDistance = Vector3.Distance(target.position, desiredPosition);
            
            // Raycast from target to desired camera position
            if (Physics.SphereCast(target.position, collisionRadius, direction, out RaycastHit hit, 
                targetDistance, collisionLayers))
            {
                // Adjust camera position to avoid collision
                float safeDistance = hit.distance - collisionOffset;
                currentDistance = Mathf.Max(safeDistance, 1f); // Minimum distance of 1 unit
                
                // Recalculate position with adjusted distance
                Quaternion rotation = Quaternion.Euler(currentY, currentX, 0);
                Vector3 offset = rotation * new Vector3(0, height, -currentDistance);
                desiredPosition = target.position + offset;
            }
            else
            {
                // Smoothly return to original distance when no collision
                currentDistance = Mathf.Lerp(currentDistance, distance, Time.deltaTime * 2f);
            }
        }
        
        private void UpdateCameraPosition()
        {
            // Smooth camera movement
            Vector3 finalPosition = Vector3.SmoothDamp(transform.position, desiredPosition, 
                ref currentVelocity, positionSmoothTime);
            
            transform.position = finalPosition;
            
            // Smooth camera rotation
            Quaternion targetRotation = Quaternion.Euler(currentY, currentX, 0);
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, 
                Time.deltaTime / rotationSmoothTime);
        }
        
        private void UpdateCameraShake()
        {
            if (shakeDuration > 0)
            {
                shakeDuration -= Time.deltaTime;
                
                // Generate random shake offset
                shakeOffset = Random.insideUnitSphere * shakeIntensity;
                shakeOffset.z = 0; // Don't shake forward/backward
                
                // Apply shake to camera position
                transform.position += shakeOffset;
                
                // Reduce shake intensity over time
                shakeIntensity = Mathf.Lerp(shakeIntensity, 0, Time.deltaTime * 2f);
            }
            else
            {
                shakeOffset = Vector3.zero;
            }
        }
        
        // Public methods for camera control
        public void SetTarget(Transform newTarget)
        {
            target = newTarget;
            if (target != null)
            {
                playerInput = target.GetComponent<PlayerInput>();
            }
        }
        
        public void ShakeCamera(float intensity, float duration)
        {
            shakeIntensity = intensity;
            shakeDuration = duration;
        }
        
        public void SetDistance(float newDistance)
        {
            distance = newDistance;
            currentDistance = newDistance;
        }
        
        public void SetHeight(float newHeight)
        {
            height = newHeight;
        }
        
        public void EnableCursor()
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        
        public void DisableCursor()
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        
        // Getters
        public Transform Target => target;
        public float CurrentDistance => currentDistance;
        public Vector3 Forward => transform.forward;
        public Vector3 Right => transform.right;
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            if (target == null) return;
            
            // Draw camera collision sphere
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, collisionRadius);
            
            // Draw line from target to camera
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(target.position, transform.position);
            
            // Draw desired position
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(desiredPosition, 0.2f);
        }
    }
}
