using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

namespace EchoesOfTheVoid.Managers
{
    /// <summary>
    /// Core game manager that handles game state, scene transitions, and system coordination
    /// Singleton pattern for global access
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        [SerializeField] private bool debugMode = true;
        [SerializeField] private float targetFrameRate = 60f;
        
        [Header("Scene Management")]
        [SerializeField] private string mainMenuScene = "MainMenu";
        [SerializeField] private string[] gameScenes = { "FracturedCitadel", "ChronoWastes", "EclipsedForest", "Voidheart" };
        [SerializeField] private int currentSceneIndex = 0;
        
        [Header("Player Settings")]
        [SerializeField] private GameObject playerPrefab;
        [SerializeField] private Transform[] playerSpawnPoints;
        
        // Singleton instance
        public static GameManager Instance { get; private set; }
        
        // Game state
        public enum GameState
        {
            MainMenu,
            Loading,
            Playing,
            Paused,
            GameOver,
            Victory
        }
        
        [SerializeField] private GameState currentState = GameState.MainMenu;
        
        // Player reference
        private GameObject currentPlayer;
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<string> OnSceneTransition;
        public System.Action OnPlayerSpawned;
        
        private void Awake()
        {
            // Singleton pattern
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetGameState(GameState.MainMenu);
        }
        
        private void Update()
        {
            HandleInput();
            
            if (debugMode)
            {
                HandleDebugInput();
            }
        }
        
        private void InitializeGame()
        {
            // Set target frame rate
            Application.targetFrameRate = (int)targetFrameRate;
            
            // Set quality settings
            QualitySettings.vSyncCount = 1;
            
            // Initialize audio settings
            AudioListener.volume = 1f;
            
            Debug.Log("Game Manager initialized");
        }
        
        private void HandleInput()
        {
            // Global input handling
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                HandleEscapeKey();
            }
        }
        
        private void HandleDebugInput()
        {
            // Debug controls
            if (Input.GetKeyDown(KeyCode.F1))
            {
                ToggleDebugMode();
            }
            
            if (Input.GetKeyDown(KeyCode.F2))
            {
                ReloadCurrentScene();
            }
            
            if (Input.GetKeyDown(KeyCode.F3))
            {
                LoadNextScene();
            }
        }
        
        private void HandleEscapeKey()
        {
            switch (currentState)
            {
                case GameState.Playing:
                    PauseGame();
                    break;
                case GameState.Paused:
                    ResumeGame();
                    break;
            }
        }
        
        public void SetGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            Debug.Log($"Game state changed from {previousState} to {newState}");
            
            // Handle state transitions
            switch (newState)
            {
                case GameState.MainMenu:
                    Time.timeScale = 1f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
                    
                case GameState.Loading:
                    Time.timeScale = 1f;
                    break;
                    
                case GameState.Playing:
                    Time.timeScale = 1f;
                    Cursor.lockState = CursorLockMode.Locked;
                    Cursor.visible = false;
                    break;
                    
                case GameState.Paused:
                    Time.timeScale = 0f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
                    
                case GameState.GameOver:
                case GameState.Victory:
                    Time.timeScale = 1f;
                    Cursor.lockState = CursorLockMode.None;
                    Cursor.visible = true;
                    break;
            }
            
            OnGameStateChanged?.Invoke(newState);
        }
        
        public void StartNewGame()
        {
            currentSceneIndex = 0;
            LoadGameScene(gameScenes[currentSceneIndex]);
        }
        
        public void LoadGameScene(string sceneName)
        {
            SetGameState(GameState.Loading);
            OnSceneTransition?.Invoke(sceneName);
            StartCoroutine(LoadSceneCoroutine(sceneName));
        }
        
        private IEnumerator LoadSceneCoroutine(string sceneName)
        {
            // Optional: Show loading screen here
            
            AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            asyncLoad.allowSceneActivation = false;
            
            // Wait for scene to load
            while (asyncLoad.progress < 0.9f)
            {
                yield return null;
            }
            
            // Optional: Minimum loading time for smooth transition
            yield return new WaitForSeconds(1f);
            
            asyncLoad.allowSceneActivation = true;
            
            // Wait for scene activation
            while (!asyncLoad.isDone)
            {
                yield return null;
            }
            
            // Scene loaded, spawn player
            SpawnPlayer();
            SetGameState(GameState.Playing);
        }
        
        public void LoadNextScene()
        {
            if (currentSceneIndex < gameScenes.Length - 1)
            {
                currentSceneIndex++;
                LoadGameScene(gameScenes[currentSceneIndex]);
            }
            else
            {
                // Game completed
                SetGameState(GameState.Victory);
            }
        }
        
        public void LoadPreviousScene()
        {
            if (currentSceneIndex > 0)
            {
                currentSceneIndex--;
                LoadGameScene(gameScenes[currentSceneIndex]);
            }
        }
        
        public void ReloadCurrentScene()
        {
            string currentScene = SceneManager.GetActiveScene().name;
            LoadGameScene(currentScene);
        }
        
        public void ReturnToMainMenu()
        {
            LoadGameScene(mainMenuScene);
            SetGameState(GameState.MainMenu);
        }
        
        private void SpawnPlayer()
        {
            if (playerPrefab == null)
            {
                Debug.LogWarning("Player prefab not assigned in GameManager");
                return;
            }
            
            // Destroy existing player
            if (currentPlayer != null)
            {
                Destroy(currentPlayer);
            }
            
            // Find spawn point
            Transform spawnPoint = GetCurrentSpawnPoint();
            Vector3 spawnPosition = spawnPoint != null ? spawnPoint.position : Vector3.zero;
            Quaternion spawnRotation = spawnPoint != null ? spawnPoint.rotation : Quaternion.identity;
            
            // Spawn player
            currentPlayer = Instantiate(playerPrefab, spawnPosition, spawnRotation);
            
            Debug.Log($"Player spawned at {spawnPosition}");
            OnPlayerSpawned?.Invoke();
        }
        
        private Transform GetCurrentSpawnPoint()
        {
            if (playerSpawnPoints != null && playerSpawnPoints.Length > currentSceneIndex)
            {
                return playerSpawnPoints[currentSceneIndex];
            }
            
            // Try to find spawn point in scene
            GameObject spawnPointObj = GameObject.FindGameObjectWithTag("PlayerSpawn");
            return spawnPointObj != null ? spawnPointObj.transform : null;
        }
        
        public void PauseGame()
        {
            SetGameState(GameState.Paused);
        }
        
        public void ResumeGame()
        {
            SetGameState(GameState.Playing);
        }
        
        public void GameOver()
        {
            SetGameState(GameState.GameOver);
        }
        
        public void Victory()
        {
            SetGameState(GameState.Victory);
        }
        
        public void QuitGame()
        {
            Debug.Log("Quitting game...");
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        private void ToggleDebugMode()
        {
            debugMode = !debugMode;
            Debug.Log($"Debug mode: {debugMode}");
        }
        
        // Public getters
        public GameState CurrentState => currentState;
        public GameObject CurrentPlayer => currentPlayer;
        public int CurrentSceneIndex => currentSceneIndex;
        public string CurrentSceneName => gameScenes.Length > currentSceneIndex ? gameScenes[currentSceneIndex] : "";
        public bool IsDebugMode => debugMode;
        
        // Save/Load system hooks (to be implemented)
        public void SaveGame()
        {
            // TODO: Implement save system
            Debug.Log("Save game functionality to be implemented");
        }
        
        public void LoadGame()
        {
            // TODO: Implement load system
            Debug.Log("Load game functionality to be implemented");
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && currentState == GameState.Playing)
            {
                PauseGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && currentState == GameState.Playing)
            {
                PauseGame();
            }
        }
    }
}
