<UXML xmlns="UnityEngine.UIElements">
    <VisualElement class="flex-container column main">
        <Label name="confirmationMessage" class="title" />

        <VisualElement name="joinSingleOrganization" class="organization-section row collapse">
            <Label name="joinSingleOrganizationLabel" class="organization-left" />
            <VisualElement class="align-vertical-center">
                <Button name="joinSingleOrganizationButton" class="organization-right" />`
            </VisualElement>
        </VisualElement>

        <VisualElement name="joinMultipleOrganizations" class="organization-section collapse">
            <Label name="joinMultipleOrganizationsLabel" />
            <VisualElement class="row">
                <VisualElement name="organizationDropdown" class="organization-left"/>
                <Button name="joinMultipleOrganizationsButton" class="organization-right" />
            </VisualElement>
        </VisualElement>

        <VisualElement name="noOrganization" class="organization-section column collapse">
            <Label name="noOrganizationLabel" class="organization-left"/>
            <Button name="openUnityDashboardButton" class="row">
                <Image name="iconUnity" />
            </Button>
        </VisualElement>

        <VisualElement name="progressContainer"/>
    </VisualElement>
</UXML>