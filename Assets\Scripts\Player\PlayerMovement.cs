using UnityEngine;

namespace EchoesOfTheVoid.Player
{
    /// <summary>
    /// Handles third-person player movement including walking, jumping, dashing, and gravity
    /// </summary>
    [RequireComponent(typeof(CharacterController))]
    public class PlayerMovement : MonoBehaviour
    {
        [Header("Movement Settings")]
        [SerializeField] private float walkSpeed = 6f;
        [SerializeField] private float runSpeed = 10f;
        [SerializeField] private float jumpHeight = 3f;
        [SerializeField] private float gravity = -9.81f;
        [SerializeField] private float groundDistance = 0.4f;
        
        [Header("Dash Settings")]
        [SerializeField] private float dashSpeed = 20f;
        [SerializeField] private float dashDuration = 0.2f;
        [SerializeField] private float dashCooldown = 1f;
        
        [Header("Double Jump")]
        [SerializeField] private bool canDoubleJump = true;
        [SerializeField] private float doubleJumpHeight = 2.5f;
        
        [Header("Ground Check")]
        [SerializeField] private Transform groundCheck;
        [SerializeField] private LayerMask groundMask = 1;
        
        // Components
        private CharacterController controller;
        private PlayerInput playerInput;
        
        // Movement state
        private Vector3 velocity;
        private bool isGrounded;
        private bool hasDoubleJumped;
        private bool isDashing;
        private float dashTimer;
        private float dashCooldownTimer;
        private Vector3 dashDirection;
        
        // Input
        private Vector2 moveInput;
        private bool jumpPressed;
        private bool dashPressed;
        private bool runPressed;
        
        private void Awake()
        {
            controller = GetComponent<CharacterController>();
            playerInput = GetComponent<PlayerInput>();
            
            // Create ground check if not assigned
            if (groundCheck == null)
            {
                GameObject groundCheckObj = new GameObject("GroundCheck");
                groundCheckObj.transform.SetParent(transform);
                groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
                groundCheck = groundCheckObj.transform;
            }
        }
        
        private void Update()
        {
            HandleInput();
            CheckGrounded();
            HandleMovement();
            HandleJumping();
            HandleDashing();
            ApplyGravity();
            
            // Apply final movement
            controller.Move(velocity * Time.deltaTime);
            
            // Update timers
            UpdateTimers();
        }
        
        private void HandleInput()
        {
            if (playerInput != null)
            {
                moveInput = playerInput.GetMoveInput();
                jumpPressed = playerInput.GetJumpPressed();
                dashPressed = playerInput.GetDashPressed();
                runPressed = playerInput.GetRunHeld();
            }
            else
            {
                // Fallback to direct input
                moveInput = new Vector2(Input.GetAxis("Horizontal"), Input.GetAxis("Vertical"));
                jumpPressed = Input.GetButtonDown("Jump");
                dashPressed = Input.GetKeyDown(KeyCode.LeftShift);
                runPressed = Input.GetKey(KeyCode.LeftControl);
            }
        }
        
        private void CheckGrounded()
        {
            isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
            
            if (isGrounded && velocity.y < 0)
            {
                velocity.y = -2f; // Small downward force to keep grounded
                hasDoubleJumped = false; // Reset double jump when grounded
            }
        }
        
        private void HandleMovement()
        {
            if (isDashing) return; // Don't handle normal movement while dashing
            
            // Calculate movement direction relative to camera
            Vector3 direction = new Vector3(moveInput.x, 0f, moveInput.y).normalized;
            
            if (direction.magnitude >= 0.1f)
            {
                // Rotate player to face movement direction
                float targetAngle = Mathf.Atan2(direction.x, direction.z) * Mathf.Rad2Deg;
                transform.rotation = Quaternion.Lerp(transform.rotation, 
                    Quaternion.AngleAxis(targetAngle, Vector3.up), Time.deltaTime * 10f);
                
                // Apply movement
                float currentSpeed = runPressed ? runSpeed : walkSpeed;
                Vector3 moveDirection = Quaternion.AngleAxis(targetAngle, Vector3.up) * Vector3.forward;
                velocity.x = moveDirection.x * currentSpeed;
                velocity.z = moveDirection.z * currentSpeed;
            }
            else
            {
                // Gradually stop movement when no input
                velocity.x = Mathf.Lerp(velocity.x, 0, Time.deltaTime * 10f);
                velocity.z = Mathf.Lerp(velocity.z, 0, Time.deltaTime * 10f);
            }
        }
        
        private void HandleJumping()
        {
            if (jumpPressed)
            {
                if (isGrounded)
                {
                    // Normal jump
                    velocity.y = Mathf.Sqrt(jumpHeight * -2f * gravity);
                }
                else if (canDoubleJump && !hasDoubleJumped)
                {
                    // Double jump
                    velocity.y = Mathf.Sqrt(doubleJumpHeight * -2f * gravity);
                    hasDoubleJumped = true;
                }
            }
        }
        
        private void HandleDashing()
        {
            // Update dash cooldown
            if (dashCooldownTimer > 0)
                dashCooldownTimer -= Time.deltaTime;
            
            // Start dash
            if (dashPressed && dashCooldownTimer <= 0 && !isDashing)
            {
                StartDash();
            }
            
            // Handle ongoing dash
            if (isDashing)
            {
                dashTimer -= Time.deltaTime;
                
                // Apply dash movement
                velocity.x = dashDirection.x * dashSpeed;
                velocity.z = dashDirection.z * dashSpeed;
                
                // End dash
                if (dashTimer <= 0)
                {
                    EndDash();
                }
            }
        }
        
        private void StartDash()
        {
            isDashing = true;
            dashTimer = dashDuration;
            dashCooldownTimer = dashCooldown;
            
            // Determine dash direction
            if (moveInput.magnitude > 0.1f)
            {
                dashDirection = new Vector3(moveInput.x, 0, moveInput.y).normalized;
            }
            else
            {
                // Dash forward if no input
                dashDirection = transform.forward;
            }
            
            // Optional: Add dash visual/audio effects here
        }
        
        private void EndDash()
        {
            isDashing = false;
            // Reduce velocity after dash
            velocity.x *= 0.5f;
            velocity.z *= 0.5f;
        }
        
        private void ApplyGravity()
        {
            if (!isDashing) // Don't apply gravity during dash
            {
                velocity.y += gravity * Time.deltaTime;
            }
        }
        
        private void UpdateTimers()
        {
            if (dashCooldownTimer > 0)
                dashCooldownTimer -= Time.deltaTime;
        }
        
        // Public getters for other systems
        public bool IsGrounded => isGrounded;
        public bool IsDashing => isDashing;
        public bool IsMoving => moveInput.magnitude > 0.1f;
        public Vector3 Velocity => velocity;
        public float DashCooldownProgress => 1f - (dashCooldownTimer / dashCooldown);
        
        // Gizmos for debugging
        private void OnDrawGizmosSelected()
        {
            if (groundCheck != null)
            {
                Gizmos.color = isGrounded ? Color.green : Color.red;
                Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
            }
        }
    }
}
