# Echoes of the Void - Unity Project

🎮 **3D Action-Adventure RPG** | Sci-Fi + Fantasy | Unity Engine

## 📖 Game Concept
You play as <PERSON><PERSON>, a young voidwalker guardian who must navigate a broken universe after a dimensional cataclysm. Traverse lost worlds, manipulate time, and battle twisted echoes of ancient beings.

## 🧩 Core Features
- ✅ Third-person movement with double jump and dash
- ✅ Real-time combat (melee + magic abilities)
- ✅ Time-manipulation powers (slow time, rewind small events)
- ✅ Puzzle-solving using time and environmental mechanics
- ✅ Leveling and ability trees
- ✅ Crafting gear with lootable resources
- ✅ Save/Load system with checkpoints

## 🕹 Controls (PC)
| Action | Key/Input |
|--------|-----------|
| Move | WASD |
| Camera | Mouse Movement |
| Jump | Space |
| Dash | Left Shift |
| Melee Attack | Left Mouse Button |
| Magic/Skill | Right Mouse Button |
| Interact | E |
| Open Inventory | I |
| Pause/Menu | Esc |

## 🌍 Game Worlds
1. **Fractured Citadel** - Tutorial level with broken palace suspended in time
2. **Chrono Wastes** - Desert of broken clocks and spiraling sandstorms
3. **Eclipsed Forest** - <PERSON> stuck in eternal dusk with shifting paths
4. **The Voidheart** - Final realm with pure black geometry and gravity shifts

## 🧙 Characters
- **<PERSON><PERSON> (Protagonist)** - Voidwalker Guardian with sword combat and time control
- **Void Echoes** - Shadowy remnants of destroyed timelines
- **Wyrms of Collapse** - Massive serpents that collapse reality
- **Timeless Watchers** - Ancient sentinels guarding time relics

## 🔧 Technical Requirements
- **Engine**: Unity (URP or HDRP)
- **Platform**: PC (Expandable to consoles)
- **Target Resolution**: 1920x1080
- **Target Framerate**: 60fps

## 📁 Project Structure
```
Assets/
├── Scripts/           # C# game logic
├── Scenes/           # Unity scene files
├── Prefabs/          # Reusable game objects
├── Materials/        # Rendering materials
├── Animations/       # Animation clips and controllers
├── Audio/            # Sound effects and music
├── UI/               # User interface assets
├── VFX/              # Visual effects
└── Fonts/            # Text fonts
```

## 🚀 Development Status
This project is currently in active development. Check the task list for current progress and upcoming milestones.

## 📅 Estimated Timeline
- **Prototype**: 2 weeks
- **Core Systems**: 1 month
- **Art Integration**: 2 months
- **Levels & Puzzles**: 1 month
- **Playtesting**: 2 weeks
- **Polish & Launch**: 2 weeks

## 🎨 Art Style
Stylized, semi-realistic with glowing magic effects and distortion. Color palette varies by biome:
- Citadel: Blue & Gold
- Wastes: Red & Yellow
- Forest: Purple & Green
- Voidheart: Black & Silver
