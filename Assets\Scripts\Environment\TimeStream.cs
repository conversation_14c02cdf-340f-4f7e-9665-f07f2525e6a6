using UnityEngine;
using System.Collections;

namespace EchoesOfTheVoid.Environment
{
    /// <summary>
    /// Time stream puzzle element for the Fractured Citadel
    /// Can be fixed using time manipulation to open paths
    /// </summary>
    public class TimeStream : MonoBehaviour
    {
        [Header("Time Stream Settings")]
        [SerializeField] private bool isBroken = true;
        [SerializeField] private float repairDuration = 3f;
        [SerializeField] private float repairRange = 5f;
        
        [Header("Connected Objects")]
        [SerializeField] private GameObject[] connectedDoors;
        [SerializeField] private GameObject[] connectedPlatforms;
        [SerializeField] private GameObject[] connectedBarriers;
        
        [Header("Visual Effects")]
        [SerializeField] private ParticleSystem brokenEffect;
        [SerializeField] private ParticleSystem repairedEffect;
        [SerializeField] private ParticleSystem repairProgressEffect;
        [SerializeField] private Material brokenMaterial;
        [SerializeField] private Material repairedMaterial;
        
        [Header("Audio")]
        [SerializeField] private AudioClip repairSound;
        [SerializeField] private AudioClip activationSound;
        [SerializeField] private AudioSource audioSource;
        
        // Components
        private Renderer streamRenderer;
        private Collider streamCollider;
        
        // State
        private bool isBeingRepaired = false;
        private float repairProgress = 0f;
        private TimeAbility playerTimeAbility;
        
        private void Awake()
        {
            streamRenderer = GetComponent<Renderer>();
            streamCollider = GetComponent<Collider>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
        
        private void Start()
        {
            UpdateVisualState();
            UpdateConnectedObjects();
        }
        
        private void Update()
        {
            if (isBroken)
            {
                CheckForPlayerTimeAbility();
                HandleRepairProcess();
            }
        }
        
        private void CheckForPlayerTimeAbility()
        {
            if (playerTimeAbility == null)
            {
                GameObject player = GameManager.Instance.CurrentPlayer;
                if (player != null)
                {
                    playerTimeAbility = player.GetComponent<TimeAbility>();
                }
            }
            
            if (playerTimeAbility != null)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, playerTimeAbility.transform.position);
                
                if (distanceToPlayer <= repairRange)
                {
                    // Check if player is using time slow ability
                    if (playerTimeAbility.IsTimeSlowed && !isBeingRepaired)
                    {
                        StartRepair();
                    }
                    else if (!playerTimeAbility.IsTimeSlowed && isBeingRepaired)
                    {
                        StopRepair();
                    }
                }
                else if (isBeingRepaired)
                {
                    StopRepair();
                }
            }
        }
        
        private void HandleRepairProcess()
        {
            if (isBeingRepaired)
            {
                repairProgress += Time.deltaTime / repairDuration;
                
                // Update repair progress effect
                if (repairProgressEffect != null)
                {
                    var main = repairProgressEffect.main;
                    main.startLifetime = repairProgress;
                }
                
                if (repairProgress >= 1f)
                {
                    CompleteRepair();
                }
            }
        }
        
        private void StartRepair()
        {
            isBeingRepaired = true;
            repairProgress = 0f;
            
            // Start repair progress effect
            if (repairProgressEffect != null)
            {
                repairProgressEffect.Play();
            }
            
            // Play repair sound
            if (repairSound != null && audioSource != null)
            {
                audioSource.clip = repairSound;
                audioSource.loop = true;
                audioSource.Play();
            }
            
            Debug.Log("Time stream repair started");
        }
        
        private void StopRepair()
        {
            isBeingRepaired = false;
            repairProgress = 0f;
            
            // Stop repair progress effect
            if (repairProgressEffect != null)
            {
                repairProgressEffect.Stop();
            }
            
            // Stop repair sound
            if (audioSource != null && audioSource.isPlaying)
            {
                audioSource.Stop();
            }
            
            Debug.Log("Time stream repair stopped");
        }
        
        private void CompleteRepair()
        {
            isBroken = false;
            isBeingRepaired = false;
            repairProgress = 1f;
            
            // Stop repair effects
            if (repairProgressEffect != null)
            {
                repairProgressEffect.Stop();
            }
            
            if (audioSource != null && audioSource.isPlaying)
            {
                audioSource.Stop();
            }
            
            // Play activation sound
            if (activationSound != null && audioSource != null)
            {
                audioSource.PlayOneShot(activationSound);
            }
            
            // Update visuals
            UpdateVisualState();
            
            // Activate connected objects
            UpdateConnectedObjects();
            
            Debug.Log("Time stream repaired successfully!");
        }
        
        private void UpdateVisualState()
        {
            if (streamRenderer != null)
            {
                streamRenderer.material = isBroken ? brokenMaterial : repairedMaterial;
            }
            
            // Update particle effects
            if (brokenEffect != null)
            {
                if (isBroken)
                    brokenEffect.Play();
                else
                    brokenEffect.Stop();
            }
            
            if (repairedEffect != null)
            {
                if (!isBroken)
                    repairedEffect.Play();
                else
                    repairedEffect.Stop();
            }
        }
        
        private void UpdateConnectedObjects()
        {
            bool shouldActivate = !isBroken;
            
            // Activate/deactivate connected doors
            foreach (GameObject door in connectedDoors)
            {
                if (door != null)
                {
                    TimeDoor timeDoor = door.GetComponent<TimeDoor>();
                    if (timeDoor != null)
                    {
                        timeDoor.SetActive(shouldActivate);
                    }
                    else
                    {
                        door.SetActive(shouldActivate);
                    }
                }
            }
            
            // Activate/deactivate connected platforms
            foreach (GameObject platform in connectedPlatforms)
            {
                if (platform != null)
                {
                    TimePlatform timePlatform = platform.GetComponent<TimePlatform>();
                    if (timePlatform != null)
                    {
                        timePlatform.SetActive(shouldActivate);
                    }
                    else
                    {
                        platform.SetActive(shouldActivate);
                    }
                }
            }
            
            // Activate/deactivate connected barriers
            foreach (GameObject barrier in connectedBarriers)
            {
                if (barrier != null)
                {
                    barrier.SetActive(!shouldActivate); // Barriers are active when stream is broken
                }
            }
        }
        
        // Public methods for external control
        public void ForceRepair()
        {
            if (isBroken)
            {
                CompleteRepair();
            }
        }
        
        public void ForceBreak()
        {
            if (!isBroken)
            {
                isBroken = true;
                isBeingRepaired = false;
                repairProgress = 0f;
                UpdateVisualState();
                UpdateConnectedObjects();
            }
        }
        
        // Public getters
        public bool IsBroken => isBroken;
        public bool IsBeingRepaired => isBeingRepaired;
        public float RepairProgress => repairProgress;
        
        // Debug visualization
        private void OnDrawGizmosSelected()
        {
            // Repair range
            Gizmos.color = isBroken ? Color.red : Color.green;
            Gizmos.DrawWireSphere(transform.position, repairRange);
            
            // Connected objects
            Gizmos.color = Color.blue;
            foreach (GameObject door in connectedDoors)
            {
                if (door != null)
                {
                    Gizmos.DrawLine(transform.position, door.transform.position);
                }
            }
            
            foreach (GameObject platform in connectedPlatforms)
            {
                if (platform != null)
                {
                    Gizmos.DrawLine(transform.position, platform.transform.position);
                }
            }
            
            foreach (GameObject barrier in connectedBarriers)
            {
                if (barrier != null)
                {
                    Gizmos.DrawLine(transform.position, barrier.transform.position);
                }
            }
        }
    }
    
    // Supporting classes for connected objects
    public class TimeDoor : MonoBehaviour
    {
        [SerializeField] private Animator doorAnimator;
        [SerializeField] private bool isOpen = false;
        
        public void SetActive(bool active)
        {
            isOpen = active;
            if (doorAnimator != null)
            {
                doorAnimator.SetBool("IsOpen", isOpen);
            }
        }
    }
    
    public class TimePlatform : MonoBehaviour
    {
        [SerializeField] private bool isActive = false;
        [SerializeField] private Vector3 activePosition;
        [SerializeField] private Vector3 inactivePosition;
        [SerializeField] private float moveSpeed = 2f;
        
        private void Start()
        {
            if (activePosition == Vector3.zero)
                activePosition = transform.position;
            
            if (inactivePosition == Vector3.zero)
                inactivePosition = transform.position + Vector3.down * 5f;
        }
        
        private void Update()
        {
            Vector3 targetPosition = isActive ? activePosition : inactivePosition;
            transform.position = Vector3.MoveTowards(transform.position, targetPosition, moveSpeed * Time.deltaTime);
        }
        
        public void SetActive(bool active)
        {
            isActive = active;
        }
    }
}
