# Quick start guide

The Version Control package will allow you to use Unity version control for your projects in the Unity Editor. 

Unity VC integrates version control in Unity that will abstract version control complexity. It will also enable you to work collaboratively on more complex projects by providing additional VCS features such as branching, locking, merging, and a standalone GUI.

The Version Control package follows the Unity support schedule. Currently, supported versions are:

* 2020.3
* 2021.3
* 2022.2
* 2023.1
* 2023.2


[Getting started with Unity version control](StartPlasticForUnity.md)
